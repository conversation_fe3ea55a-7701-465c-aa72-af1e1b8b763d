<template>
	<view class="privacy-list">
		<view class="item">
			<navigator url="/pages/users/privacy/index?type=3" hover-class="none" class="item-arrow">
				<view class="item-content">
					<view class="item-title">{{$t(`隐私协议`)}}</view>
					<text class="iconfont icon-xiangyou"></text>
				</view>
			</navigator>
		</view>
		<view class="item">
			<navigator url="/pages/users/privacy/index?type=4" hover-class="none" class="item-arrow">
				<view class="item-content">
					<view class="item-title">{{$t(`用户协议`)}}</view>
					<text class="iconfont icon-xiangyou"></text>
				</view>
			</navigator>
		</view>
	</view>
</template>

<script>
</script>

<style scoped>
	.privacy-list {
		background-color: #ffffff;
		padding: 0 32rpx;
	}

	.item {
		border-bottom: 1rpx solid #f5f5f5;
		height: 112rpx;
	}

	.item:last-child {
		border-bottom: none;
	}

	.item-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 100%;
		cursor: pointer;
	}

	.item-title {
		color: #141414;
		font-weight: 400;
		font-size: 28rpx;
		line-height: 40rpx;
	}

	.item-arrow {
		width: 100%;
		height: 100%;
	}

	.item-arrow .iconfont {
		font-size: 28rpx;
		color: #cccccc;
	}

	/* 点击态效果 */
	.item-content:active {
		background-color: #f8f8f8;
	}
</style>